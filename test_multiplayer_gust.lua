-- 测试联机多玩家季风乱流事件
-- 在游戏控制台运行: dofile("mods/thinking/test_multiplayer_gust.lua")

print("=== 联机多玩家季风乱流测试 ===")

local world = TheWorld
if not world or not world.components.seasonal_gust_manager then
    print("错误：找不到季风乱流管理器")
    return
end

local gust = world.components.seasonal_gust_manager

-- 显示所有在线玩家
print("当前在线玩家:")
local players = AllPlayers or {}
if #players == 0 and ThePlayer then
    players = {ThePlayer}
    print("单机模式，只有一个玩家")
end

for i, player in ipairs(players) do
    if player and player:IsValid() then
        local name = player:GetDisplayName() or ("玩家" .. i)
        local x, y, z = player.Transform:GetWorldPosition()
        local is_ghost = player:HasTag("playerghost")
        print(string.format("  %d. %s 位置:(%.1f, %.1f) %s", 
            i, name, x, z, is_ghost and "[幽灵]" or "[活着]"))
    end
end

print("\n当前季节:", world.state.season)
print("活跃事件:", gust.active_event and "有" or "无")

-- 如果有活跃事件，先结束它
if gust.active_event then
    print("结束当前活跃事件...")
    gust:EndEvent()
end

-- 触发新事件
print("\n触发季风乱流事件...")
gust:StartEvent()

-- 等待2秒后检查宝珠生成情况
TheWorld:DoTaskInTime(2, function()
    print("\n=== 宝珠生成检查 ===")
    
    for i, player in ipairs(players) do
        if player and player:IsValid() and not player:HasTag("playerghost") then
            local name = player:GetDisplayName() or ("玩家" .. i)
            local px, py, pz = player.Transform:GetWorldPosition()
            
            -- 查找该玩家附近的宝珠
            local orbs = TheSim:FindEntities(px, py, pz, 10, {"season_orb"})
            print(string.format("%s 附近的宝珠数量: %d", name, #orbs))
            
            for j, orb in ipairs(orbs) do
                if orb and orb:IsValid() then
                    local ox, oy, oz = orb.Transform:GetWorldPosition()
                    local dist = math.sqrt((px-ox)^2 + (pz-oz)^2)
                    print(string.format("  宝珠%d: %s, 距离: %.1f", j, orb.prefab, dist))
                end
            end
        end
    end
    
    print("\n=== 测试完成 ===")
    print("每个活着的玩家附近都应该有3-5个宝珠")
    print("玩家说话应该显示了'天降神珠！'")
    print("\n手动结束事件: TheWorld.components.seasonal_gust_manager:EndEvent()")
end)
