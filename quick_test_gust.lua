-- 快速测试季风乱流事件
-- 在游戏控制台运行: dofile("mods/thinking/quick_test_gust.lua")

print("=== 快速测试季风乱流事件 ===")

-- 检查基础组件
local player = ThePlayer
local world = TheWorld

if not player then
    print("错误：找不到玩家")
    return
end

if not world or not world.components.seasonal_gust_manager then
    print("错误：找不到季风乱流管理器")
    return
end

local gust = world.components.seasonal_gust_manager

-- 显示当前状态
print("当前季节:", world.state.season)
print("事件管理器状态:", gust.enabled and "启用" or "禁用")
print("活跃事件:", gust.active_event and "有" or "无")

-- 测试宝珠创建
local season = world.state.season
local orb_prefab = "season_orb_" .. season
local test_orb = SpawnPrefab(orb_prefab)

if test_orb then
    print("✓ " .. season .. "季宝珠创建成功")
    local x, y, z = player.Transform:GetWorldPosition()
    test_orb.Transform:SetPosition(x + 2, 0, z + 2)
    print("宝珠已放置在玩家附近")
else
    print("✗ 宝珠创建失败")
end

-- 手动触发事件
print("\n触发季风乱流事件...")
if gust.active_event then
    gust:EndEvent()
end

gust:StartEvent()

if gust.active_event then
    print("✓ 事件启动成功")
    print("事件将持续", gust.active_event.duration, "秒")
else
    print("✗ 事件启动失败")
end

print("\n=== 测试完成 ===")
print("请检查周围是否有发光的季节宝珠")
print("靠近宝珠可自动吸收获得碎片")
