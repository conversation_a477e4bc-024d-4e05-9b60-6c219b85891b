-- 季风乱流事件测试脚本
-- 在游戏控制台中运行: dofile("mods/thinking/test_seasonal_gust.lua")

local function TestSeasonalGust()
    print("=== 季风乱流事件测试开始 ===")
    
    -- 获取玩家和世界组件
    local player = ThePlayer or AllPlayers[1]
    if not player then
        print("错误：找不到玩家")
        return
    end
    
    local world = TheWorld
    if not world or not world.components.seasonal_gust_manager then
        print("错误：找不到世界或季风乱流管理器组件")
        return
    end
    
    local gust_manager = world.components.seasonal_gust_manager
    
    print("1. 测试基础组件状态")
    print("   - 季风乱流管理器已加载:", gust_manager ~= nil)
    print("   - 当前季节:", world.state.season)
    print("   - 管理器启用状态:", gust_manager.enabled)
    print("   - 当前活跃事件:", gust_manager.active_event ~= nil)
    
    -- 测试宝珠prefab是否正确加载
    print("\n2. 测试季节宝珠prefab")
    local seasons = {"spring", "summer", "autumn", "winter"}
    for _, season in ipairs(seasons) do
        local orb_prefab = "season_orb_" .. season
        local test_orb = SpawnPrefab(orb_prefab)
        if test_orb then
            print("   ✓ " .. season .. "季宝珠创建成功")
            test_orb:Remove() -- 立即清理测试宝珠
        else
            print("   ✗ " .. season .. "季宝珠创建失败")
        end
    end
    
    -- 测试碎片prefab
    print("\n3. 测试季节碎片prefab")
    for _, season in ipairs(seasons) do
        local shard_prefab = "season_shard_" .. season
        local test_shard = SpawnPrefab(shard_prefab)
        if test_shard then
            print("   ✓ " .. season .. "季碎片创建成功")
            test_shard:Remove() -- 立即清理测试碎片
        else
            print("   ✗ " .. season .. "季碎片创建失败")
        end
    end
    
    -- 手动触发季风乱流事件
    print("\n4. 手动触发季风乱流事件")
    if gust_manager.active_event then
        print("   当前已有活跃事件，先结束它")
        gust_manager:EndEvent()
    end
    
    -- 记录玩家初始位置和背包状态
    local px, py, pz = player.Transform:GetWorldPosition()
    local initial_inventory_count = 0
    if player.components.inventory then
        for i = 1, player.components.inventory.maxslots do
            if player.components.inventory.itemslots[i] then
                initial_inventory_count = initial_inventory_count + 1
            end
        end
    end
    
    print("   - 玩家位置: (" .. px .. ", " .. pz .. ")")
    print("   - 初始背包物品数量:", initial_inventory_count)
    
    -- 触发事件
    gust_manager:StartEvent()
    
    -- 检查事件是否成功启动
    if gust_manager.active_event then
        print("   ✓ 季风乱流事件成功启动")
        print("   - 事件季节:", gust_manager.active_event.season)
        print("   - 事件持续时间:", gust_manager.active_event.duration, "秒")
    else
        print("   ✗ 季风乱流事件启动失败")
        return
    end
    
    -- 检查附近是否生成了宝珠
    print("\n5. 检查宝珠生成情况")
    local orbs = TheSim:FindEntities(px, py, pz, 10, {"season_orb"})
    print("   - 找到宝珠数量:", #orbs)
    
    for i, orb in ipairs(orbs) do
        if orb and orb:IsValid() then
            local ox, oy, oz = orb.Transform:GetWorldPosition()
            local dist = math.sqrt((px-ox)^2 + (pz-oz)^2)
            print("   - 宝珠" .. i .. ": " .. orb.prefab .. ", 距离: " .. string.format("%.1f", dist))
            
            -- 检查宝珠的发光效果
            if orb.components.light then
                print("     * 发光组件: ✓, 半径:" .. orb.components.light.radius .. ", 强度:" .. orb.components.light.intensity)
            else
                print("     * 发光组件: ✗")
            end
        end
    end
    
    -- 测试宝珠吸收功能
    if #orbs > 0 then
        print("\n6. 测试宝珠吸收功能")
        local test_orb = orbs[1]
        if test_orb and test_orb:IsValid() then
            -- 移动玩家到宝珠附近
            local ox, oy, oz = test_orb.Transform:GetWorldPosition()
            player.Transform:SetPosition(ox + 1, oy, oz) -- 移动到宝珠旁边
            
            print("   - 移动玩家到宝珠附近")
            print("   - 等待自动吸收...")
            
            -- 等待几秒让吸收逻辑执行
            player:DoTaskInTime(2, function()
                -- 检查宝珠是否被吸收
                if not test_orb:IsValid() then
                    print("   ✓ 宝珠已被成功吸收")
                else
                    print("   ✗ 宝珠未被吸收")
                end
                
                -- 检查背包中是否有新的碎片
                local final_inventory_count = 0
                if player.components.inventory then
                    for i = 1, player.components.inventory.maxslots do
                        if player.components.inventory.itemslots[i] then
                            final_inventory_count = final_inventory_count + 1
                        end
                    end
                end
                
                local gained_items = final_inventory_count - initial_inventory_count
                print("   - 获得物品数量:", gained_items)
                
                -- 检查是否获得了季节碎片
                local current_season = world.state.season
                local expected_shard = "season_shard_" .. current_season
                if player.components.inventory:Has(expected_shard, 1) then
                    print("   ✓ 成功获得" .. current_season .. "季碎片")
                else
                    print("   ✗ 未获得预期的季节碎片")
                end
                
                -- 测试事件结束
                print("\n7. 测试事件结束机制")
                if gust_manager.active_event then
                    print("   - 手动结束事件")
                    gust_manager:EndEvent()
                    
                    if not gust_manager.active_event then
                        print("   ✓ 事件成功结束")
                    else
                        print("   ✗ 事件结束失败")
                    end
                else
                    print("   - 事件已自动结束")
                end
                
                print("\n=== 季风乱流事件测试完成 ===")
            end)
        end
    else
        print("\n6. 跳过吸收测试（没有生成宝珠）")
        print("\n=== 季风乱流事件测试完成 ===")
    end
end

-- 运行测试
TestSeasonalGust()
